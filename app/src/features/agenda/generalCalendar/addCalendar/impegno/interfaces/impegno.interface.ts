export interface IDeadlineParams {
    deadlineUniqueid: string;
    deadlineLinkuid?: string;
    deadlineFileUniqueid: string;
    templates?: string;
    dynamic: string;
    macroInstanceUid?: string;
    recurrenceId?: string;
    connectedElements?: string;
    idPratica?: string;
    deadlineText: string;
    deadlineDate: string;
    deadlineHours: string;
    deadlineMinutes: string;
    deadlineDaysBefore: number;
    deadlinePeriod: string;
    deadlineOnorariDiritti: string;
    deadlineSpeseesenti: string;
    deadlineSpeseimponibili: string;
    deadlineSpeseescluse: string;
    deadlineType: string;
    deadlineCategory: string;
    deadlineStatus: string;
    deadLinesGroups: any;
    deadlineUser: any;
    deadlineAnnotation?: string;
    deadlineAddebitabile: boolean;
    parentItemId: string;
    deadlineUserData?: string;
    nuovoImpegno: string;
    deadlineUniqueidToDeclare: string;
    deadlineVisible: boolean;
    deadlineOptions?: string;
    deadlineEvasa?: boolean;
    deadlineNonevadere?: boolean;
    deadlineImportant?: boolean;
    deadlineBillable?: boolean;
    deadlinePrivate?: boolean;
    endType: string;
    occurrencesN: string;
    FREQ?: string;
    UNTIL?: string;
    INTERVAL?: string;
    rirtemplate: string;
    rirangebyenddatecalendar: string;
    riweeklyinterval: string;
    rimonthlyinterval: string;
    rimonthlydayofmonthday: string;
    riyearlyinterval: string;
    riyearlydayofmonthmonth: string;
    ridailyinterval: string;
    deadlineFile?: string;
    riweeklyweekdaysSU?: string;
    riweeklyweekdaysMO?: string;
    riweeklyweekdaysTU?: string;
    riweeklyweekdaysWE?: string;
    riweeklyweekdaysTH?: string;
    riweeklyweekdaysFR?: string;
    riweeklyweekdaysSA?: string;
    BYDAY?: string;
    [key: string]: any;
}

export interface ISchedaImpegnoProps {
  data: any;
  practicaSearchResult: any;
  setPracticaSearchResult: React.Dispatch<React.SetStateAction<any>>;
  handlePracticaSearch: (value: any) => void;
  searchPracticaLoading: boolean;
  impegnoSearchResult: any;
  handleImpegnoSearch: (value: any) => void;
  searchImpegnoLoading: boolean;
  deadlineSaveParams: IDeadlineParams;
  setDeadlineSaveParams: React.Dispatch<React.SetStateAction<IDeadlineParams>>;
  fetchUserGroupById: (id: string) => void;
  loggedUserName: string;
  fetchCalendarData: () => void;
  setIsArchiveChecked: React.Dispatch<React.SetStateAction<boolean>>;
  requiredFields: any;
  setRequiredFields: React.Dispatch<React.SetStateAction<any>>;
  selectedPraticaName: any | null;
  setSelectedPraticaName: React.Dispatch<React.SetStateAction<any | null>>;
  enablePratichaInput?: boolean;
  hideFileSelect?: boolean;
}

export interface IAnagrafiche {
  insertAnagraficheParams: any;
  setInsertAnagraficheParams: React.Dispatch<React.SetStateAction<any>>;
  anagraficheQuery: any;
  setAnagraficheQuery: React.Dispatch<React.SetStateAction<any>>;
  listAnagrafiche: any;
  loading: boolean;
  handleAnagraficheSearch: (arg: any) => void;
  anagraficheResult: any;
  searchAnagraficheLoading: boolean;
  selectedAnagrafica: string | null;
  setSelectedAnagrafica: React.Dispatch<React.SetStateAction<string | null>>;
  handleAddAnagraficheOnCreate: () => void;
  isUserEditing: boolean;
  handleAddAnagraficheOnUpdate: () => void;
}
