import { Box, TextField, FormControl, InputLabel, FormControlLabel, Checkbox, IconButton, Select, MenuItem, Button, Typography, Stack } from "@vapor/react-material";
import { DatePicker } from "../../../../../../components/ui-kit/DatePicker";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ConfirmModal from "../../../../../../custom-components/ConfirmModal";
import moment from "moment";
import { createFilterOptions } from "@mui/material/Autocomplete";
import useSaveDeadlineTypes from "../hooks/useSaveDeadlineTypes";
import { HOURS, MINUTES } from "../constants/constant";
import { ISchedaImpegnoProps } from "../interfaces/impegno.interface";
import useSessionStorageState from "../hooks/useSessionStorageState";
import TextInputWithClearButton from "../../../../../../custom-components/TextInputWithClearButton";
import useDeadlineSelection from "../hooks/useDeadlineSelection";
import { parseDate } from "../../../../../../helpers/parseDataFormat";
import { removeLinks } from "../../../../../../utilities/utils";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import { useTranslation } from "@1f/react-sdk";
import CustomAutocomplete from "../../../../../../custom-components/CustomAutocomplete";

const filter = createFilterOptions<any>();
const ICON = <CheckBoxOutlineBlankIcon fontSize="small" />;
const CHECKED_ICON = <CheckBoxIcon fontSize="small" />;

export default function SchedaImpegno(props: ISchedaImpegnoProps) {
    const {
        data,
        practicaSearchResult,
        setPracticaSearchResult,
        handlePracticaSearch,
        searchPracticaLoading,
        impegnoSearchResult,
        handleImpegnoSearch,
        searchImpegnoLoading,
        deadlineSaveParams,
        setDeadlineSaveParams,
        fetchUserGroupById,
        loggedUserName,
        fetchCalendarData,
        setIsArchiveChecked,
        requiredFields,
        setRequiredFields,
        selectedPraticaName,
        setSelectedPraticaName,
    } = props;

    const { t } = useTranslation();
    const { saveDeadlineType, saveDeadlineCategoria, saveDeadlineStato } =
        useSaveDeadlineTypes();
    const {
        selectedTipologia,
        setSelectedTipologia,
        selectedCategoria,
        setSelectedCategoria,
        selectedStato,
        setSelectedStato,
        clearSessionStorage,
    } = useSessionStorageState();
    const [showModal, setShowModal] = useState<boolean>(false);
    const [tempUsersSelected, setTempUsersSelected] = useState<any>([]);

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: value });
    };

    const handleInputImpegnoSearch = (_: any, newInputValue: any) => {
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineText: newInputValue.descrizione,
        });
    };

    const handlePraticaInputChanges = (_: any, newInputValue: any) => {
        setSelectedPraticaName(newInputValue);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineFileUniqueid: newInputValue.uniqueid,
        });
    };

    const handleInputCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: checked });
    };

    const handleRequiredInputs = (event: any) => {
        const { name, value } = event.target;
        setRequiredFields({ ...requiredFields, [name]: false });
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: value });
    };

    const onDateChange = (name: string, value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        setDeadlineSaveParams((prevValue: any) => ({
            ...prevValue,
            [name]: formattedDate,
        }));
    };

    // Function to set checked users based on the selected group
    const setCheckedUsersFromGroup = (groupData: any) => {
        const checkedUsers = groupData.map((checkedUser: any) =>
            data.usersData.find((user: any) => user.id === checkedUser.userId)
        );
        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: checkedUsers,
        }));
    };

    const handleIncrementDecrement = (delta: number) => {
        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineDaysBefore: Math.max(
                0,
                parseInt(prevParams.deadlineDaysBefore) + delta
            ),
        }));
    };

    const handleUsersGroup = async (_: any, newValue: any) => {
        const { id } = newValue;

        if (id !== "-1") {
            try {
                const groups = await fetchUserGroupById(id);

                // Check if groups is an array and has items
                if (
                    Array.isArray(groups) &&
                    groups.length > 0 &&
                    requiredFields.deadlineUser
                ) {
                    setRequiredFields({
                        ...requiredFields,
                        deadlineUser: false,
                    });
                }

                setDeadlineSaveParams((prevParams) => ({
                    ...prevParams,
                    deadLinesGroups: newValue,
                    deadlineUser: Array.isArray(groups) ? groups : [], // Ensure groups is an array
                }));

                if (Array.isArray(groups)) {
                    setCheckedUsersFromGroup(groups);
                }
            } catch (error) {
                console.error("Error fetching user group:", error);
                // Handle error case
            }
        } else {
            setDeadlineSaveParams((prevParams) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti..."),
                },
                deadlineUser: id === "-1" ? [] : prevParams.deadlineUser, // Keep existing users if not resetting
            }));
        }
    };

    const updateDeadlineUsers = (users: any) => {
        const uniqueUsers = Array.from(
            new Map(users.map((user: any) => [user.id, user])).values()
        );

        // Directly update the `deadlineUser` with the unique list
        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: uniqueUsers,
        }));
    };

    const handleUserData = (_: any, newValue: any) => {
        // Clear validation error if users are selected
        if (newValue && newValue.length > 0 && requiredFields.deadlineUser) {
            setRequiredFields({
                ...requiredFields,
                deadlineUser: false,
            });
        }

        if (
            deadlineSaveParams.deadLinesGroups &&
            (deadlineSaveParams.deadLinesGroups === "-1" ||
                deadlineSaveParams.deadLinesGroups.id === "-1")
        ) {
            // Ensure uniqueness by `id` only
            updateDeadlineUsers(newValue);
        } else {
            setTempUsersSelected(newValue);
            setShowModal(true);
        }
    };

    const handleModalConfirm = (confirm: boolean) => {
        if (confirm) {
            setDeadlineSaveParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti..."),
                },
            }));

            if (tempUsersSelected.length > 0) {
                updateDeadlineUsers(tempUsersSelected);
            }
        }
        setShowModal(false);
    };

    const siglaRapidaAnnotazioni = () => {
        const timestamp = `${loggedUserName} ${moment().format(
            "DD.MM.YYYY HH:mm:ss"
        )}`;

        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineAnnotation: prevParams.deadlineAnnotation
                ? `${prevParams.deadlineAnnotation}\n${timestamp}`
                : timestamp,
        }));
    };

    const handleTipologiaChange = async (_event: any, value: any) => {
        setSelectedTipologia(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineType: value.id,
        });
        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineType(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const handleCategoriaChange = async (_event: any, value: any) => {
        setSelectedCategoria(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineCategory: value.id,
        });
        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineCategoria(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const handleStatoChange = async (_event: any, value: any) => {
        setSelectedStato(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineStatus: value.id,
        });
        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineStato(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const getOptionLabelFunc = (option: any) => {
        if (typeof option === "string") {
            return option;
        }
        // Ensure the input box shows only the actual text, not "Aggiungi ..."
        if (option.nome.startsWith(t('Aggiungi "'))) {
            return option.inputValue;
        }
        return option.nome;
    };

    const filterOptionsFunction = (options: any, params: any) => {
        const filtered = filter(options, params);
        const { inputValue } = params;
        const isExisting = options.some(
            (option: any) => inputValue === option.nome
        );
        if (inputValue !== "" && !isExisting) {
            filtered.push({
                nome: `Aggiungi "${inputValue}"`,
                inputValue: inputValue, // Keep the raw input value
            });
        }
        return filtered;
    };

    const renderOptionFucntion = (props: any, option: any) => {
        if (option.nome && option.nome.startsWith('Aggiungi "')) {
            return (
                <li {...props}>
                    <Typography>{option.nome}</Typography>
                </li>
            );
        }
        return <li {...props}>{option.nome}</li>;
    };

    useDeadlineSelection({
        deadlineSaveParams,
        data,
        setSelectedTipologia,
        setSelectedCategoria,
        setSelectedStato,
        selectedTipologia,
        selectedCategoria,
        selectedStato,
        clearSessionStorage,
        setDeadlineSaveParams,
    });

    return (
        <div style={{ display: "flex", gap: "100px", marginLeft: "10px" }}>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                }}
            >
                {!props.hideFileSelect && (
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            gap: 1,
                        }}
                    >
                        <FormControl sx={{ width: 500 }}>
                            <CustomAutocomplete
                                options={practicaSearchResult}
                                selectOnFocus
                                clearOnBlur
                                limitTags={1}
                                loading={searchPracticaLoading}
                                value={selectedPraticaName}
                                loadingText={t("Caricamento...")}
                                noOptionsText={t("Nessuna opzione")}
                                getOptionLabel={(option: any) => {
                                    if (
                                        !option.listaclienti &&
                                        !option.listacontroparti
                                    ) {
                                        return option.codicearchivio
                                            ? `${option.codicearchivio}: Nessun cliente / Nessuna controparte`
                                            : "";
                                    }
                                    return removeLinks(
                                        option.headerArchive
                                            ? option.headerArchive
                                            : `${
                                                  option.listaclienti || ""
                                              } contro ${
                                                  option.listacontroparti || ""
                                              }`,
                                        " "
                                    );
                                }}
                                onChange={(event: any, newInputValue: any) =>
                                    handlePraticaInputChanges(
                                        event,
                                        newInputValue
                                    )
                                }
                                onInputChange={(_: any, newInputValue: any) => {
                                    if (newInputValue === "") {
                                        setPracticaSearchResult([]);
                                        return;
                                    }
                                    handlePracticaSearch(newInputValue);
                                }}
                                renderOption={(props: any, option: any) => (
                                    <div {...props}>
                                        <span>
                                            {removeLinks(
                                                option.headerArchive,
                                                " "
                                            )}
                                        </span>
                                    </div>
                                )}
                                renderInput={(params: any) => (
                                    <TextField
                                        label={t("Pratica")}
                                        {...params}
                                        placeholder={t(
                                            "Cerca pratica per codice, descrizione, nominativi, RG…"
                                        )}
                                        InputProps={{
                                            ...params.InputProps,
                                            endAdornment: (
                                                <>
                                                    {
                                                        params.InputProps
                                                            .endAdornment
                                                    }
                                                </>
                                            ),
                                        }}
                                    />
                                )}
                            />
                        </FormControl>

                        <IconButton
                            color="primary"
                            onClick={() => {
                                setDeadlineSaveParams({
                                    ...deadlineSaveParams,
                                    deadlineFileUniqueid: "",
                                });
                                setSelectedPraticaName(null);
                            }}
                            sx={{
                                ml: 1,
                                mt: 3.5,
                            }}
                        >
                            <FontAwesomeIcon
                                icon={faTrashCan}
                                color="red"
                                size="lg"
                            />
                        </IconButton>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    onChange={(e: any) =>
                                        setIsArchiveChecked(e.target.checked)
                                    }
                                />
                            }
                            label={t("Archiviate")}
                            sx={{ mr: 2, mt: 3.5 }}
                        />
                    </Box>
                )}
                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        options={impegnoSearchResult}
                        selectOnFocus
                        clearOnBlur
                        limitTags={1}
                        loadingText={t("Caricamento...")}
                        noOptionsText={t("Nessuna opzione")}
                        value={deadlineSaveParams?.deadlineOptions || null}
                        loading={searchImpegnoLoading}
                        onChange={(event: any, newInputValue: any) =>
                            handleInputImpegnoSearch(event, newInputValue)
                        }
                        getOptionLabel={(option: any) => {
                            return option.descrizione;
                        }}
                        onInputChange={(_: any, newInputValue: any) => {
                            handleImpegnoSearch(newInputValue);
                        }}
                        renderInput={(params: any) => (
                            <TextField
                                {...params}
                                label={t("Cerca")}
                                placeholder={t("Cerca impegno per nome")}
                            />
                        )}
                    />
                </FormControl>
                <FormControl sx={{ width: 500 }}>
                    <TextField
                        name="deadlineText"
                        error={!!requiredFields.deadlineText}
                        helperText={
                            requiredFields.deadlineText
                                ? t("Oggetto obbligatorio")
                                : ""
                        }
                        multiline
                        rows={3}
                        label={t("Oggetto")}
                        value={deadlineSaveParams?.deadlineText}
                        onChange={handleRequiredInputs}
                        sx={{
                            width: 500,
                            ml: 1,
                        }}
                    />
                </FormControl>

                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "row",
                        gap: 1,
                        width: 508,
                        mb: 2,
                    }}
                >
                    <DatePicker
                        label={t("Data")}
                        name="deadlineDate"
                        value={parseDate(deadlineSaveParams?.deadlineDate)}
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('deadlineDate', date);
                            }
                        }}
                        sx={{
                            "& .MuiTextField-root": {
                                width: "100px !important",
                            },
                            mt: "2px !important",
                        }}
                    />
                    <FormControl sx={{ width: 80, mt: "2px" }}>
                        <InputLabel>{t("Ora")}</InputLabel>
                        <Select
                            name="deadlineHours"
                            value={deadlineSaveParams?.deadlineHours}
                            onChange={handleInputChanges}
                        >
                            {HOURS.map((hour: any) => (
                                <MenuItem key={hour} value={hour}>
                                    {hour}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <FormControl sx={{ width: 80, mt: "2px" }}>
                        <InputLabel id="hour-label">{t("Min")}</InputLabel>
                        <Select
                            name="deadlineMinutes"
                            value={String(
                                deadlineSaveParams?.deadlineMinutes
                            ).padStart(2, "0")}
                            onChange={handleInputChanges}
                        >
                            {MINUTES.map((min: any) => (
                                <MenuItem key={min} value={min}>
                                    {min}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
                <Box
                    alignItems="center"
                    sx={{
                        "& .MuiTextField-root": {
                            m: 0,

                            width: 350,
                        },
                    }}
                >
                    <Stack
                        alignItems="center"
                        component="form"
                        direction="row"
                        spacing={0}
                        sx={{
                            width: 350,
                            gap: 0,
                            ml: 1,
                        }}
                    >
                        <Typography
                            variant="body500"
                            component="div"
                            color="primary.main"
                            gutterBottom
                            sx={{ pr: 1 }}
                        >
                            {t("Avvisa ")}
                        </Typography>
                        <Button
                            variant="contained"
                            size="small"
                            onClick={() => handleIncrementDecrement(-1)}
                            sx={{ minWidth: "30px", padding: "5px" }}
                        >
                            <KeyboardArrowLeftIcon />
                        </Button>
                        <TextField
                            size="small"
                            name="deadlineDaysBefore"
                            value={deadlineSaveParams?.deadlineDaysBefore}
                            onChange={handleInputChanges}
                            type="number"
                        />
                        <Button
                            variant="contained"
                            size="small"
                            onClick={() => handleIncrementDecrement(+1)}
                            sx={{ minWidth: "30px", padding: "5px" }}
                        >
                            <KeyboardDoubleArrowRightIcon />
                        </Button>
                        <Typography
                            variant="body500"
                            component="div"
                            color="primary.main"
                            gutterBottom
                            sx={{ pl: 1, whiteSpace: "nowrap" }}
                        >
                            {t("Giorni Prima")}
                        </Typography>
                    </Stack>
                </Box>

                <Box
                    alignItems="center"
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 265,
                        },
                    }}
                >
                    <FormControl sx={{ width: 80 }}>
                        <TextField
                            label={t("Durata")}
                            name="deadlinePeriod"
                            error={!!requiredFields.deadlinePeriod}
                            helperText={
                                requiredFields.deadlinePeriod
                                    ? t("Durata obbligatorio")
                                    : ""
                            }
                            value={deadlineSaveParams.deadlinePeriod}
                            onChange={handleRequiredInputs}
                            onKeyDown={(e) => {
                                if (e.key === "-") {
                                    e.preventDefault();
                                }
                            }}
                            type="number"
                        />
                    </FormControl>
                </Box>

                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        options={data?.tipologiaData}
                        value={selectedTipologia}
                        onChange={handleTipologiaChange}
                        selectOnFocus
                        clearOnBlur
                        isOptionEqualToValue={(option: any, value: any) => {
                            return option.id === value.id;
                        }}
                        getOptionLabel={getOptionLabelFunc}
                        filterOptions={filterOptionsFunction}
                        renderOption={renderOptionFucntion}
                        renderInput={(params: any) => (
                            <TextInputWithClearButton
                                params={params}
                                selectedValue={selectedTipologia}
                                setSelectedValue={setSelectedTipologia}
                                label={t("Tipologia")}
                                placeholder={t("Selezonia una tipologia")}
                            />
                        )}
                    />
                </FormControl>
                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        sx={{ width: "100%" }}
                        options={data?.categoriaData || []}
                        selectOnFocus
                        clearOnBlur
                        limitTags={1}
                        value={selectedCategoria}
                        loadingText={t("Caricamento...")}
                        noOptionsText={t("Nessuna opzione")}
                        onChange={handleCategoriaChange}
                        filterOptions={filterOptionsFunction}
                        renderOption={renderOptionFucntion}
                        getOptionLabel={getOptionLabelFunc}
                        renderInput={(params: any) => (
                            <TextInputWithClearButton
                                params={params}
                                selectedValue={selectedCategoria}
                                setSelectedValue={setSelectedCategoria}
                                label={t("Categoria")}
                                placeholder={t("Selezonia una categoria")}
                            />
                        )}
                    />
                </FormControl>
            </Box>

            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                }}
            >
                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        sx={{ width: "100%" }}
                        options={data?.statoData || []}
                        selectOnFocus
                        noOptionsText={t("Nessuna opzione")}
                        clearOnBlur
                        limitTags={1}
                        value={selectedStato}
                        onChange={handleStatoChange}
                        filterOptions={filterOptionsFunction}
                        renderOption={renderOptionFucntion}
                        getOptionLabel={getOptionLabelFunc}
                        renderInput={(params: any) => (
                            <TextInputWithClearButton
                                params={params}
                                selectedValue={selectedStato}
                                setSelectedValue={setSelectedStato}
                                label={t("Stato")}
                                placeholder={t("Selezonia una stato")}
                            />
                        )}
                    />
                </FormControl>
                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        options={[
                            {
                                id: "-1",
                                name: t("Seleziona il gruppo di utenti..."),
                            },
                            ...(data?.groupsData || []),
                        ]}
                        value={
                            deadlineSaveParams.deadLinesGroups === "-1"
                                ? {
                                      id: "-1",
                                      name: t(
                                          "Seleziona il gruppo di utenti..."
                                      ),
                                  }
                                : deadlineSaveParams.deadLinesGroups
                        }
                        isOptionEqualToValue={(option: any, value: any) => {
                            return option.id === value.id;
                        }}
                        selectOnFocus
                        clearOnBlur
                        limitTags={1}
                        noOptionsText={t("Nessuna opzione")}
                        onChange={handleUsersGroup}
                        getOptionLabel={(option: any) => option?.name}
                        renderInput={(params: any) => (
                            <TextField {...params} label={t("Gruppo Utenti")} />
                        )}
                    />
                </FormControl>
                <FormControl sx={{ width: 500 }}>
                    <CustomAutocomplete
                        disableCloseOnSelect
                        clearOnEscape
                        multiple
                        options={data.usersData || []}
                        value={deadlineSaveParams.deadlineUser}
                        onChange={handleUserData}
                        isOptionEqualToValue={(option: any, value: any) => {
                            return option.id === value.id;
                        }}
                        getOptionLabel={(option: any) => option.nomeutente}
                        renderInput={(params: any) => (
                            <TextField
                                {...params}
                                label={t("Intestatari")}
                                error={!!requiredFields.deadlineUser}
                                helperText={
                                    requiredFields.deadlineUser
                                        ? t(
                                              "Selezionare almeno un intestatario"
                                          )
                                        : ""
                                }
                                onChange={() => {
                                    if (requiredFields.deadlineUser) {
                                        setRequiredFields({
                                            ...requiredFields,
                                            deadlineUser: false,
                                        });
                                    }
                                }}
                            />
                        )}
                        noOptionsText={t("Nessuna opzione")}
                        renderOption={(props: any, option: any) => {
                            const isChecked =
                                deadlineSaveParams?.deadlineUser.some(
                                    (user: any) => user?.id === option.id
                                );
                            return (
                                <li key={option.nomeutente} {...props}>
                                    <Checkbox
                                        icon={ICON}
                                        checkedIcon={CHECKED_ICON}
                                        style={{ marginRight: 8 }}
                                        checked={isChecked}
                                    />
                                    {option.nomeutente}
                                </li>
                            );
                        }}
                    />
                </FormControl>
                <Box sx={{ display: "flex", flexDirection: "row", gap: 2 }}>
                    <FormControl sx={{ width: 500 }}>
                        <TextField
                            label={t("Annotazioni")}
                            name="deadlineAnnotation"
                            value={deadlineSaveParams.deadlineAnnotation}
                            onChange={handleInputChanges}
                            multiline
                            minRows={3}
                            fullWidth
                        />
                    </FormControl>
                    <Button
                        variant="contained"
                        size="small"
                        sx={{ marginTop: "35px" }}
                        onClick={siglaRapidaAnnotazioni}
                    >
                        {t("Sigla")}
                    </Button>
                </Box>
                <Box
                    display="flex"
                    justifyContent="space-between"
                    width="100%"
                    sx={{ ml: 1, mt: 1 }}
                >
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlineEvasa"
                                    checked={deadlineSaveParams?.deadlineEvasa}
                                    disabled={
                                        deadlineSaveParams?.deadlineNonevadere ===
                                        true
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Evaso")}
                        />
                    </Box>
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlineNonevadere"
                                    checked={
                                        deadlineSaveParams?.deadlineNonevadere
                                    }
                                    disabled={
                                        deadlineSaveParams.deadlineEvasa ===
                                        true
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Da non evadere")}
                        />
                    </Box>
                </Box>

                <Box
                    display="flex"
                    justifyContent="space-between"
                    width="100%"
                    sx={{ ml: 1 }}
                >
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlineImportant"
                                    checked={
                                        deadlineSaveParams?.deadlineImportant
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Importante")}
                        />
                    </Box>
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlineVisible"
                                    checked={
                                        deadlineSaveParams?.deadlineVisible
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Visibile ad utenti esterni")}
                        />
                    </Box>
                </Box>
                <Box
                    display="flex"
                    flexDirection="row"
                    alignItems="center"
                    marginTop="16px"
                >
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlinePrivate"
                                    checked={
                                        deadlineSaveParams?.deadlinePrivate
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Privato")}
                        />
                    </Box>
                </Box>

                <Box
                    display="flex"
                    justifyContent="space-between"
                    width="100%"
                    sx={{ ml: 1 }}
                >
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    defaultChecked
                                    name="deadlineAddebitabile"
                                    checked={
                                        deadlineSaveParams?.deadlineAddebitabile
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Addebitabile")}
                        />
                    </Box>
                    <Box display="flex" flex="1">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="deadlineBillable"
                                    checked={
                                        deadlineSaveParams?.deadlineBillable
                                    }
                                    onChange={handleInputCheckboxChanges}
                                />
                            }
                            label={t("Fatturabile")}
                        />
                    </Box>
                </Box>
            </Box>
            <ConfirmModal
                open={showModal}
                handleDecline={() => handleModalConfirm(false)}
                handleAgree={() => handleModalConfirm(true)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t(
                    "Stai associando manualmente le persone al gruppo. L'associazione di gruppo verra tolta."
                )}
                title={t("Vuoi continuare?")}
            />
        </div>
    );
}
