import React from "react";
import { renderCalendarText } from "./calendarHelper";
import { EventTooltip } from "./tooltipCustomHelper";
import { Typography, Grid } from "@vapor/react-material";
import { ICustomEventUIProps } from "../typings/generalCalendar.interface";
import GoogleIcon from "./../../../../assets/images/bulletGoogle.png";
import OutlookIcon from "./../../../../assets/images/bulletOutlook.png";
import { format } from 'date-fns';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/pro-regular-svg-icons';

export const CustomEventUI = (props: ICustomEventUIProps) => {
    const {
        event,
        query,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId,
        navigate,
        location,
        items,
        t,
    } = props;

    const eventData = event.event._def.extendedProps;
    const isPrivateEvent = eventData.private === "1" || eventData.private === 1 || eventData.isPrivateAndVisible;



    let dataToRender = renderCalendarText(
        event,
        query.calendarReferent,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId
    );

    // Format event time for display
    const formatEventTime = (eventStart: string | Date) => {
        if (!eventStart) return '';
        try {
            const date = new Date(eventStart);
            return format(date, 'HH:mm');
        } catch (error) {
            return '';
        }
    };

    const isMonthlyView = query?.viewName === 'month' || query?.viewName === 'dayGridMonth';

    const eventStyle = {
        backgroundColor: event.allDay ? '#D1D5DB' : event.backgroundColor,
        whiteSpace: "normal",
        color: event.allDay ? '#222222' : "black",
        ...(event.allDay && {
            border: '1px solid #BBBDC0',
            borderRadius: '2px',
            padding: '2px 4px',
            fontWeight: 600,
            height: '26px'
        })
    };


    const handleNavigateToEvent = () => {
        // Check if this is a private event that user cannot edit
        const eventData = event.event._def.extendedProps;
        if (eventData.canEdit === false) {
            return; // Don't navigate for private events user cannot edit
        }

        const event_id = eventData.uniqueid;
        const typeEvent = eventData.type;

        if (typeEvent === "hearing" && event_id) {
            navigate(`/agenda/agenda/update/${event_id}`, {
                state: {
                    origin: "agenda",
                    type: "update",
                    uniqueId: event_id,
                    items: items,
                    row: { praticaUid: eventData.fileUid },
                    prevPath: location.pathname ?? "/calendar/calendar",
                    defaultParams: query,
                    rowDataUrl: 'deadlines',
                },
            });
        } else if (event_id) {
            navigate(`/impegno/update/${event_id}`, {
                state: {
                    prevPath: location.pathname ?? "/calendar/calendar",
                    defaultParams: query,
                },
            });
        }
    };
    return (
        <EventTooltip
            title={
                <React.Fragment>
                    <Grid container spacing={1}>
                        <Grid item xs={12}>
                            {event?.event?._def.extendedProps.type ===
                            "polisweb" ? (
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                    <Typography variant="caption" sx={{
                                        fontFamily: 'Roboto',
                                        fontWeight: 500,
                                        fontSize: '16px',
                                        lineHeight: '16px',
                                        letterSpacing: '0%',
                                        verticalAlign: 'middle',
                                        flex: 1
                                    }}>
                                        {isMonthlyView
                                            ? (event.event._def.title.length > 10
                                                ? event.event._def.title.substring(0, 15) + '...'
                                                : event.event._def.title)
                                            : event.event._def.title
                                        }
                                    </Typography>
                                    {isPrivateEvent && (
                                        // <LockIcon style={{ fontSize: '12px', marginLeft: '4px', verticalAlign: 'middle' }} />
                                        <FontAwesomeIcon icon={faLock}></FontAwesomeIcon>
                                    )}
                                </div>
                            ) : (
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                    <Typography variant="caption" sx={{
                                        fontFamily: 'Roboto',
                                        fontWeight: 500,
                                        fontSize: '16px',
                                        lineHeight: '16px',
                                        letterSpacing: '0%',
                                        verticalAlign: 'middle',
                                        flex: 1
                                    }}>
                                        {isMonthlyView
                                            ? (event.event._def.title.length > 10
                                                ? event.event._def.title.substring(0, 15) + '...'
                                                : event.event._def.title)
                                            : event.event._def.title
                                        }
                                    </Typography>
                                    {isPrivateEvent && (
                                        // <LockIcon style={{ fontSize: '12px', marginLeft: '4px', verticalAlign: 'middle' }} />
                                        <FontAwesomeIcon icon={faLock}></FontAwesomeIcon>
                                    )}
                                </div>
                            )}
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sx={{ display: "flex", flexDirection: "column" }}
                        >
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: dataToRender.tooltip,
                                }}
                            />
                        </Grid>
                    </Grid>
                </React.Fragment>
            }
        >
             <Grid container spacing={1}>
                <Grid item xs={12}>
                    <div
                        style={{
                            ...eventStyle,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            position: "relative",
                            cursor: event.event._def.extendedProps.canEdit !== false ? "pointer" : "default",
                        }}
                        onClick={handleNavigateToEvent}
                    >
                <div style={{
                    padding: '4px',
                    fontSize: '11px',
                    lineHeight: '1.2',
                    overflow: 'visible',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    paddingRight: isMonthlyView && typeof event.event.extendedProps.calendarioEsternoType !== "undefined" &&
                        event.event.extendedProps.calendarioEsternoType !== "" &&
                        event.event.extendedProps.calendarioEsternoType !== null ? '25px' : '4px'
                }}>
                    <div style={{
                        position: 'relative',
                        fontFamily: 'Roboto',
                        fontWeight: 500,
                        fontSize: '16px',
                        lineHeight: '16px',
                        letterSpacing: '0%',
                        verticalAlign: 'middle',
                        marginBottom: '2px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '100%'
                    }}>
                        <span
                            dangerouslySetInnerHTML={{ __html: dataToRender.icons }}
                        />
                        {isMonthlyView && formatEventTime(event.event.start) && (
                            <span style={{
                                fontFamily: 'Roboto',
                                fontWeight: 400,
                                fontSize: '10px',
                                lineHeight: '12px',
                                letterSpacing: '0%',
                                verticalAlign: 'middle',
                                marginRight: '4px'
                            }}>
                                {formatEventTime(event.event.start)}
                               
                            </span>
                           
                        )}
                        {isMonthlyView
                            ? (event.event.title.length > 10
                                ? event.event.title.substring(0, 15) + '...'
                                : event.event.title)
                            : event.event.title
                        }
                        {isPrivateEvent && (
                            <FontAwesomeIcon icon={faLock} style={{ marginLeft: '4px' }} />
                        )}
                        
                    </div>

                    {event.event.extendedProps?.autorita && (
                        <div style={{
                            fontFamily: 'Roboto',
                            fontWeight: 400,
                            fontSize: '10px',
                            lineHeight: '12px',
                            letterSpacing: '0%',
                            verticalAlign: 'middle',
                            marginBottom: '1px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            width: '100%'
                        }}>
                            {event.event.extendedProps.autorita}
                        </div>
                    )}

                    {(event.event.extendedProps?.referenti || event.event.extendedProps?.avvocato) && (
                        <div style={{
                            marginBottom: '1px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            width: '100%'
                        }}>
                            <span style={{
                                fontFamily: 'Roboto',
                                fontWeight: 600,
                                fontSize: '10px',
                                lineHeight: '12px',
                                letterSpacing: '0%',
                                verticalAlign: 'middle'
                            }}>{t("Intestatari")}:</span>{' '}
                            <span style={{
                                fontFamily: 'Roboto',
                                fontWeight: 400,
                                fontSize: '10px',
                                lineHeight: '12px',
                                letterSpacing: '0%',
                                verticalAlign: 'middle'
                            }}>{event.event.extendedProps?.referenti || event.event.extendedProps?.avvocato || 'N/A'}</span>
                        </div>
                    )}

                    {event.event.extendedProps?.annotazioni && (
                        <div style={{
                            marginBottom: '1px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            width: '100%'
                        }}>
                            <span style={{
                                fontFamily: 'Roboto',
                                fontWeight: 600,
                                fontSize: '10px',
                                lineHeight: '12px',
                                letterSpacing: '0%',
                                verticalAlign: 'middle'
                            }}>{t("Annotazioni")}:</span>{' '}
                            <span style={{
                                fontFamily: 'Roboto',
                                fontWeight: 400,
                                fontSize: '10px',
                                lineHeight: '12px',
                                letterSpacing: '0%',
                                verticalAlign: 'middle'
                            }}>{event.event.extendedProps.annotazioni}</span>
                        </div>
                    )}

                    <div style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '100%'
                    }}>
                        <span style={{
                            fontFamily: 'Roboto',
                            fontWeight: 600,
                            fontSize: '10px',
                            lineHeight: '12px',
                            letterSpacing: '0%',
                            verticalAlign: 'middle'
                        }}>{t("Dettagli")}:</span>{' '}
                        <span style={{
                            fontFamily: 'Roboto',
                            fontWeight: 400,
                            fontSize: '10px',
                            lineHeight: '12px',
                            letterSpacing: '0%',
                            verticalAlign: 'middle'
                        }}>
                            {event.event.extendedProps?.danonevadere === '1'
                                ? 'da non evadere'
                                : event.event.extendedProps?.evasa === '1'
                                    ? 'evaso'
                                    : 'da evadere'
                            }
                        </span>
                    </div>
                      <div style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '100%',
                        marginTop: '5px',
                    }}>
                        
                        {event.event.extendedProps?.important === '1' && (
                            <>
                                <div style={{ position: 'relative', padding: '10px'}}>
                                <span style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: 0,
                                    height: 0,
                                    borderTop: '10px solid red',
                                    borderRight: '10px solid transparent',
                                }}></span>

                                
                                <strong style={{ verticalAlign: 'middle', display: 'ruby-text', marginLeft: '5px' }}>
                                    {t("Importante")}
                                </strong>
                                </div>
                            </>
                        )} 
                    </div>


                </div>

                {/* Calculate positioning for icons */}
                {(() => {
                    const hasExternalCalendar = typeof event.event.extendedProps.calendarioEsternoType !== "undefined" &&
                        event.event.extendedProps.calendarioEsternoType !== "" &&
                        event.event.extendedProps.calendarioEsternoType !== null;

                    return (
                        <>
                            {/* Lock icon for private events */}
                            {isPrivateEvent && (
                                <FontAwesomeIcon icon={faLock} />
                            )}

                            {/* External calendar icons */}
                            {isMonthlyView && hasExternalCalendar && (
                                <img
                                    style={{
                                        position: "absolute",
                                        top: "2px",
                                        right: "2px",
                                        width: "18px",
                                        height: "15px",
                                        zIndex: 1,
                                    }}
                                    src={
                                        event.event.extendedProps.calendarioEsternoType === "1"
                                            ? GoogleIcon
                                            : event.event.extendedProps.calendarioEsternoType === "2"
                                            ? OutlookIcon
                                            : ""
                                    }
                                />
                            )}
                            {!isMonthlyView && hasExternalCalendar && (
                                <img
                                    style={{
                                        position: "absolute",
                                        top: "2px",
                                        right: "2px",
                                        width: "16px",
                                        height: "14px",
                                        zIndex: 1,
                                    }}
                                    src={
                                        event.event.extendedProps.calendarioEsternoType === "1"
                                            ? GoogleIcon
                                            : event.event.extendedProps.calendarioEsternoType === "2"
                                            ? OutlookIcon
                                            : ""
                                    }
                                />
                            )}
                        </>
                    );
                })()}
            </div>
            </Grid>
            </Grid>
        </EventTooltip>
    );
};
