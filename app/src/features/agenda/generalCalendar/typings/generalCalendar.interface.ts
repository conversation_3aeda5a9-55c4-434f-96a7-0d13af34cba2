import { EventInput } from "@fullcalendar/core";
import FullCalendar from "@fullcalendar/react";
import { MutableRefObject, ReactNode } from "react";
import { NavigateFunction, Location } from "react-router-dom";

interface BaseCalendarProps {
    DEFAULT_QUERY: IQuery | IQueryMonthlyReport;
    query: IQuery;
    setQuery: React.Dispatch<React.SetStateAction<IQuery>>;
    fetchEventData?: (query?: IQuery) => void;
    setMonthTitle: React.Dispatch<React.SetStateAction<string | undefined>>;
    t: any;
}

export interface IQueryMonthlyReport {
    currentDate: string;
    id: string;
    viewName: string;
    dailyWorkload: string;
    noTemplateVars: boolean;
}

export interface IQuery {
    start: number;
    end: number;
    viewName: string;
    date: number;
    calendarCommitments: string;
    calendarPersons: string[];
    calendarGroup: string;
    calendarReferent: string;
    calendarReferents: string[];
    calendarAuthorities: string;
    calendarEvasa: string;
    calendarDeadlineType: string;
    calendarNonevadere: string;
    calendarWeekends: boolean;
    agendaView: boolean;
    onlyGroupEvents: boolean;
    deadlinesNotice: string;
    hearingsNotice: string;
    authoritySearchId: string;
    pratica: string;
    deadlineType: number;
    deadlineCategory: number;
    praticaSelectUniqueid: string;
    authority: string;
    visStart: number;
    visEnd: number;
    closeDeadline: boolean;
    calendarDeadlineCategory: string;
}

export interface ICalendarProps extends BaseCalendarProps {
    calendarRef: MutableRefObject<FullCalendar | null>;
    eventData: ICalendarEvent[];
    eventResponse?: any;
    monthTitle: string | undefined;
    setMonthTitle: React.Dispatch<React.SetStateAction<string | undefined>>;
    items: any[];
    calendarData: any;
    onViewChange?: (viewName: string) => void;
}

export interface ICalendarButtonsProps extends BaseCalendarProps {
    calendarRef: MutableRefObject<FullCalendar | null>;
    monthTitle?: string | undefined;
    setMonthTitle: React.Dispatch<React.SetStateAction<string | undefined>>;
    children?: ReactNode;
    leftPanelOpen?: boolean;
    setLeftPanelOpen?: (open: boolean) => void;
    rightPanelOpen?: boolean;
    setRightPanelOpen?: (open: boolean) => void;
    selectedView?: string;
    onViewChange?: (viewName: string) => void;
    calendarData?: any;
    eventData?: any;
    eventResponse?: any;
    openImpegnoModal?: () => void;
    setRightPanelType?: any;
    rightPanelType?: string;
    showAddForm?: boolean;
    setShowAddForm?: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface ICalendarFilterProps extends BaseCalendarProps {
    calendarData: any;
    calendarRef: any;
}

export interface ICustomEventUIProps {
    event: EventInput;
    query: IQuery;
    loggedUserCampiAgenda: string | null;
    impegniMultiutente: string | null;
    netlexSettingsFileId: string | null;
    navigate: NavigateFunction;
    location: Location;
    items?: any[];
    t: (key: string) => string;
}

export interface IPrintTemplatesRespose {
    id: string;
    title: string;
    filename: string;
    user_id: string;
    upload_date: string;
    uniqueid: string;
    category: string;
    default_print: string;
    signature_points: null | number[];
}

export interface ICalendarEvent {
    id: string;
    pratica: string | null;
    contract_id: string | null;
    descrizionepratica: string | null;
    nome_pratica: string | null;
    durata: string;
    ora: string;
    title: string;
    evasa: string;
    polisweb: string;
    annotazioni: string;
    uniqueid: string;
    modificatoil: string;
    avviso: string | null;
    ggmancanti: string | null;
    important: string;
    danonevadere: string;
    dataUdienza: string | null;
    dataUltimaUdienza: string;
    ruologeneralenumero: string | null;
    ruologeneraleanno: string | null;
    subprocedimento: string | null;
    rgnr: string | null;
    rgnranno: string | null;
    rggip: string | null;
    rggipanno: string | null;
    rggup: string | null;
    rggupanno: string | null;
    rgtrib: string | null;
    rgtribanno: string | null;
    rgapp: string | null;
    rgappanno: string | null;
    rgcass: string | null;
    rgcassanno: string | null;
    rgsiep: string | null;
    rgsiepanno: string | null;
    rgsius: string | null;
    rgsiusanno: string | null;
    rgriesame: string | null;
    rgriesameanno: string | null;
    nomeStatoPratica: string;
    avvocato: string;
    tipologia: string;
    type: string;
    citta: string | null;
    autorita: string | null;
    sezione: string | null;
    istruttore: string | null;
    codicearchivio: string | null;
    fileUid: string | null;
    dinamica: string | null;
    fkUtenteGruppo: string | null;
    nomepratica: string | null;
    start: string;
    end?: string | Date;
    status: string;
    className: string;
    editable: string;
    color: string;
    calendarioEsternoType: string | null;
    isOld: string;
    listaclienti: string | null;
    listacontroparti: string | null;
    nomeutente: string;
    sigla: string | null;
    referenti: string;
    subject: string;
    full_day: string;
    private: string | number;
}
